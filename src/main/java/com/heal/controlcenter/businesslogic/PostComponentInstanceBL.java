package com.heal.controlcenter.businesslogic;

import com.heal.configuration.pojos.Account;
import com.heal.configuration.pojos.CompInstClusterDetails;
import com.heal.configuration.pojos.Service;
import com.heal.controlcenter.beans.*;
import com.heal.controlcenter.dao.mysql.AccountServiceDao;
import com.heal.controlcenter.dao.mysql.AccountsDao;
import com.heal.controlcenter.dao.mysql.CompInstanceDao;
import com.heal.controlcenter.dao.mysql.MasterDataDao;
import com.heal.controlcenter.dao.mysql.entity.*;
import com.heal.controlcenter.pojo.Attributes;
import com.heal.controlcenter.pojo.Controller;
import com.heal.configuration.pojos.BasicEntity;
import com.heal.controlcenter.beans.CompInstanceAttributesBean;
import com.appnomic.appsone.common.util.StringUtils;
import com.heal.controlcenter.dao.redis.AccountRepo;
import com.heal.controlcenter.dao.redis.InstanceRepo;
import com.heal.controlcenter.dao.redis.ServiceRepo;
import com.heal.controlcenter.exception.ClientException;
import com.heal.controlcenter.exception.DataProcessingException;
import com.heal.controlcenter.exception.HealControlCenterException;
import com.heal.controlcenter.exception.ServerException;
import com.heal.controlcenter.pojo.ComponentInstancePojo;
import com.heal.controlcenter.pojo.IdPojo;
import com.heal.controlcenter.util.*;
import com.heal.controlcenter.aop.LogExecutionAnnotation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.jdbc.support.GeneratedKeyHolder;
import org.springframework.jdbc.support.KeyHolder;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.sql.PreparedStatement;
import java.sql.Statement;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Business logic for creating component instances.
 * Handles validation, database operations, and Redis cache updates.
 */
@Slf4j
@Component
public class PostComponentInstanceBL implements BusinessLogic<List<ComponentInstancePojo>, UtilityBean<List<ComponentInstanceBean>>, List<IdPojo>> {

    private final ClientValidationUtils clientValidationUtils;
    private final ServerValidationUtils serverValidationUtils;
    private final ComponentInstanceDao componentInstanceDao;
    private final CompInstanceDao compInstanceDao;
    private final AccountsDao accountsDao;
    private final AccountServiceDao accountServiceDao;
    private final MasterDataDao masterDataDao;
    private final MasterComponentDao masterComponentDao;
    private final AgentDao agentDao;
    private final EnvironmentDao environmentDao;
    private final AccountRepo accountRepo;
    private final ServiceRepo serviceRepo;
    private final InstanceRepo instanceRepo;
    private final TagMappingBL tagMappingBL;
    private final ComponentDao componentDao;

    public PostComponentInstanceBL(ClientValidationUtils clientValidationUtils,
                                   ServerValidationUtils serverValidationUtils,
                                   ComponentInstanceDao componentInstanceDao,
                                   CompInstanceDao compInstanceDao,
                                   AccountsDao accountsDao,
                                   AccountServiceDao accountServiceDao,
                                   MasterDataDao masterDataDao,
                                   MasterComponentDao masterComponentDao,
                                   AgentDao agentDao,
                                   EnvironmentDao environmentDao,
                                   AccountRepo accountRepo,
                                   ServiceRepo serviceRepo,
                                   InstanceRepo instanceRepo,
                                   TagMappingBL tagMappingBL,
                                   ComponentDao componentDao) {
        this.clientValidationUtils = clientValidationUtils;
        this.serverValidationUtils = serverValidationUtils;
        this.componentInstanceDao = componentInstanceDao;
        this.compInstanceDao = compInstanceDao;
        this.accountsDao = accountsDao;
        this.accountServiceDao = accountServiceDao;
        this.masterDataDao = masterDataDao;
        this.masterComponentDao = masterComponentDao;
        this.agentDao = agentDao;
        this.environmentDao = environmentDao;
        this.accountRepo = accountRepo;
        this.serviceRepo = serviceRepo;
        this.instanceRepo = instanceRepo;
        this.tagMappingBL = tagMappingBL;
        this.componentDao = componentDao;
    }

    /**
     * Validates the client-side input for creating component instances.
     * @param requestBody List of ComponentInstancePojo objects
     * @param requestParams Authorization and account identifier
     * @return UtilityBean containing validated request data
     * @throws ClientException if validation fails
     */
    @Override
    @LogExecutionAnnotation
    public UtilityBean<List<ComponentInstancePojo>> clientValidation(List<ComponentInstancePojo> requestBody, String... requestParams) throws ClientException {
        String accountIdentifier = requestParams[0];
        
        // Validate account identifier
        clientValidationUtils.accountIdentifierValidation(accountIdentifier);
        
        // Validate request body
        if (requestBody == null || requestBody.isEmpty()) {
            throw new ClientException("Component instance request list cannot be null or empty");
        }

        // Validate each component instance request
        Map<String, String> errors = new HashMap<>();
        for (int i = 0; i < requestBody.size(); i++) {
            ComponentInstancePojo request = requestBody.get(i);
            request.validate();
            if (!request.getError().isEmpty()) {
                int finalI = i;
                request.getError().forEach((key, value) ->
                    errors.put("componentInstances[" + finalI + "]." + key, value));
            }
        }

        if (!errors.isEmpty()) {
            String errorMessage = errors.toString();
            log.error("Component instance validation failed: {}", errorMessage);
            throw new ClientException(errorMessage);
        }

        HashMap<String, String> requestParamsMap = CommonUtils.buildRequestParams(null, accountIdentifier);
        log.debug("[clientValidation] Validation successful for accountIdentifier: {}", accountIdentifier);

        return UtilityBean.<List<ComponentInstancePojo>>builder()
                .requestParams(requestParamsMap)
                .pojoObject(requestBody)
                .metadata(new HashMap<>())
                .build();
    }

    /**
     * Validates server-side constraints and prepares data for processing.
     * @param utilityBean Validated client data
     * @return UtilityBean with ComponentInstanceBean objects ready for processing
     * @throws ServerException if server validation fails
     */
    @Override
    @LogExecutionAnnotation
    public UtilityBean<List<ComponentInstanceBean>> serverValidation(UtilityBean<List<ComponentInstancePojo>> utilityBean) throws ServerException {
        try {
            String accountIdentifier = utilityBean.getRequestParams().get(Constants.ACCOUNT_IDENTIFIER);
            List<ComponentInstancePojo> requests = utilityBean.getPojoObject();

            // Validate account exists
            Account account = serverValidationUtils.accountValidation(accountIdentifier);
            if (account == null) {
                throw new ServerException("Account not found with identifier: " + accountIdentifier);
            }

            // Get user ID from metadata
            String userId = (String) utilityBean.getMetadata().get(Constants.USER_ID_KEY);

            // Convert requests to beans with comprehensive server validations (following appsone-controlcenter pattern)
            List<ComponentInstanceBean> componentInstanceBeans = addServerValidations(requests, userId, account);

            utilityBean.getMetadata().put(Constants.ACCOUNT, account);
            
            return UtilityBean.<List<ComponentInstanceBean>>builder()
                    .requestParams(utilityBean.getRequestParams())
                    .pojoObject(componentInstanceBeans)
                    .metadata(utilityBean.getMetadata())
                    .build();

        } catch (Exception e) {
            log.error("Server validation failed: {}", e.getMessage(), e);
            throw new ServerException("Server validation failed: " + e.getMessage());
        }
    }

    /**
     * Processes the component instance creation - equivalent to ComponentInstanceBL.process in appsone-controlcenter.
     * This method handles both database insertion and Redis cache updates.
     * @param utilityBean Validated component instance data
     * @return List of created component instance IDs
     * @throws DataProcessingException if processing fails
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    @LogExecutionAnnotation
    public List<IdPojo> process(UtilityBean<List<ComponentInstanceBean>> utilityBean) throws DataProcessingException {
        try {
            List<ComponentInstanceBean> componentInstances = utilityBean.getPojoObject();
            Account account = (Account) utilityBean.getMetadata().get(Constants.ACCOUNT);

            // Process component instances - equivalent to ComponentInstanceBL.process
            return processComponentInstances(componentInstances, account.getIdentifier());

        } catch (Exception e) {
            log.error("Error occurred while processing component instances: {}", e.getMessage(), e);
            throw new DataProcessingException("Error occurred while processing component instances: " + e.getMessage());
        }
    }

    /**
     * Processes component instances - exact equivalent of ComponentInstanceBL.process in appsone-controlcenter.
     * This method follows the exact pattern from the original code:
     * 1. Process each component instance in transaction (equivalent to dbi.inTransaction)
     * 2. Add instances to Redis (equivalent to ComponentInstanceUtil.addInstancesToRedis)
     * @param beanList List of validated component instance beans
     * @param accountIdentifier Account identifier
     * @return List of created instance IDs
     * @throws Exception if processing fails
     */
    private List<IdPojo> processComponentInstances(List<ComponentInstanceBean> beanList, String accountIdentifier) throws Exception {

        // Process each component instance in transaction (equivalent to dbi.inTransaction)
        for (ComponentInstanceBean componentInstanceBean : beanList) {
            addComponentInstance(componentInstanceBean);
        }

        // Add instances to Redis - equivalent to ComponentInstanceUtil.addInstancesToRedis
        return addInstancesToRedis(beanList, accountIdentifier);
    }

    /**
     * Adds component instance with KPIs - equivalent to ComponentInstanceBL.addComponentInstance.
     * @param bean Component instance bean
     * @return Instance ID
     * @throws Exception if insertion fails
     */
    private void addComponentInstance(ComponentInstanceBean bean) throws Exception {
        int instanceId = addComponentInstanceToDatabase(bean);
        if (instanceId != -1) {
            if (bean.getIsUpdate() == 0) {
                // Add config watch KPIs - equivalent to addConfigWatchKPIs
                addConfigWatchKPIs(bean, instanceId);
            } else {
                log.info("Updated comp instance for identifier:{}, name :{}", bean.getIdentifier(), bean.getName());
            }
        }
    }

    /**
     * Adds config watch KPIs for component instance - equivalent to addConfigWatchKPIs in original.
     * Converted from JDBI to JDBC pattern following appsone-controlcenter repository.
     * @param bean Component instance bean
     * @param instanceId Instance ID
     * @throws HealControlCenterException if KPI addition fails
     */
    private void addConfigWatchKPIs(ComponentInstanceBean bean, int instanceId) throws HealControlCenterException {
        try {
            List<CompInstanceKpiGroupDetailsBean> kpiList = componentDao.getConfigWatchFilesByComponentId(bean.getMstComponentId(), bean.getMstComponentVersionId());
            if (kpiList != null && !kpiList.isEmpty()) {
                for (CompInstanceKpiGroupDetailsBean kpiBean : kpiList) {
                    kpiBean.setCompInstanceId(instanceId);
                    kpiBean.setCreatedTime(DateTimeUtil.getTimeInGMT(System.currentTimeMillis()));
                    kpiBean.setUpdatedTime(DateTimeUtil.getTimeInGMT(System.currentTimeMillis()));
                    kpiBean.setUserDetailsId(bean.getUserDetailsId());
                    kpiBean.setStatus(1);
                    kpiBean.setAliasName(kpiBean.getAttributeValue() == null ? Constants.ALL : kpiBean.getAttributeValue());

                    // Use JDBC-based approach instead of JDBI handle
                    int id = addGroupComponentInstanceKPI(kpiBean);
                    if (id == -1) {
                        String err = "Unable to add Group KPIs -" + kpiBean.getMstKpiDetailsId() + " for component instance id-" + instanceId;
                        log.error(err);
                        throw new HealControlCenterException(err);
                    }
                    log.info("Added config/file watch KPIs: {}, attribute: {}, for component instance id: {}", kpiBean.getMstKpiDetailsId(),
                            kpiBean.getAttributeValue(), instanceId);
                }
            } else {
                log.info("No config/file watch KPIs found for component instance id: {}, component id:{}, component version id: {}",
                        instanceId, bean.getMstComponentId(), bean.getMstComponentVersionId());
            }
        } catch (Exception e) {
            log.error("Error adding config watch KPIs for component instance id: {}", instanceId, e);
            throw e;
        }
    }

    /**
     * Adds a single group component instance KPI using JDBC instead of JDBI.
     * Equivalent to CompInstanceDataService.addGroupComponentInstanceKPI from appsone-controlcenter.
     * This method follows the JDBC pattern used in appsone-controlcenter for returning generated IDs.
     * @param kpiBean The KPI bean to add
     * @return The generated ID from the database, or -1 if failed
     */
    private int addGroupComponentInstanceKPI(CompInstanceKpiGroupDetailsBean kpiBean) {
        try {
            if (kpiBean.getAliasName() == null || kpiBean.getAliasName().trim().isEmpty()) {
                kpiBean.setAliasName(kpiBean.getAttributeValue());
            }
            // Use the new JDBC method that returns generated ID
            return compInstanceDao.addSingleGroupCompInstanceKpiDetail(kpiBean);
        } catch (Exception e) {
            log.error("Error occurred while adding group component instance KPI for component instance id: {}. Details: {}",
                    kpiBean.getCompInstanceId(), e.getMessage(), e);
            return -1;
        }
    }

    /**
     * Comprehensive server validations following the original appsone-controlcenter pattern.
     * This method performs all the validations that were done in ComponentInstanceBL.addServerValidations
     * and ComponentInstanceUtil.validateAndGetComponentInstance.
     */
    private List<ComponentInstanceBean> addServerValidations(List<ComponentInstancePojo> instances, String userId, Account account) throws ServerException {
        try {
            List<ComponentInstanceBean> beanList = new ArrayList<>();

            // Get service list for validation (equivalent to CommonUtils.getControllersByTypeBypassCache(Constants.SERVICES_CONTROLLER_TYPE, accId))
            List<Controller> serviceList = getControllersByTypeBypassCache(Constants.SERVICES_CONTROLLER_TYPE, account.getId());

            for (ComponentInstancePojo instance : instances) {
                ComponentInstanceBean bean = validateAndGetComponentInstance(instance, userId, account.getId(), serviceList, new ArrayList<>());
                beanList.add(bean);
            }

            // Perform additional validations for multiple instances (moreValidationsForEachFields equivalent)
            if (!beanList.isEmpty()) {
                performCrossInstanceValidations(beanList);
            }

            return beanList;

        } catch (Exception e) {
            log.error("Error in server validations: {}", e.getMessage(), e);
            throw new ServerException("Server validation failed: " + e.getMessage());
        }
    }

    /**
     * Validates and converts a single ComponentInstancePojo to ComponentInstanceBean.
     * Exact equivalent of ComponentInstanceUtil.validateAndGetComponentInstance from appsone-controlcenter.
     * Converted from JDBI to JDBC.
     */
    private ComponentInstanceBean validateAndGetComponentInstance(ComponentInstancePojo instance, String userId, int accountId,
                                                                 List<Controller> serviceList, List<Integer> agentIdsList) throws ServerException {
        String err;
        int isHost = 0;
        int isPod = 0;

        // Get component details with name and version (equivalent to ComponentDataService.getComponentDetailsWithNameandVersion)
        MasterComponentBean componentBean = masterComponentDao.getComponentDetailsWithNameAndVersion(
                instance.getComponentName(), instance.getComponentVersion(), accountId);

        if (componentBean == null) {
            err = "Component with name '" + instance.getComponentName() + "' and version '" + instance.getComponentVersion() + "' doesn't exist.";
            log.error(err);
            throw new ServerException(err);
        }

        // Get host component type (equivalent to MasterCache.getMasterComponentTypeUsingName)
        MasterComponentTypeBean componentTypeBean = masterComponentDao.getMasterComponentTypeUsingName("Host", String.valueOf(accountId));

        if (componentTypeBean == null) {
            err = "Component with type 'Host' doesn't exist.";
            log.error(err);
            throw new ServerException(err);
        }

        int hostComponentTypeId = componentTypeBean.getId();

        if (componentTypeBean.getId() == componentBean.getComponentTypeId()) {
            isHost = 1;
        } else {
            componentTypeBean = masterComponentDao.getMasterComponentTypeUsingName("Pod", String.valueOf(accountId));

            if (componentTypeBean != null && componentTypeBean.getId() == componentBean.getComponentTypeId()) {
                isPod = 1;
            }
        }

        int[] serviceId = null;

        if (serviceList != null) {
            serviceId = getServiceIds(serviceList, instance.getServiceIdentifiers());
        }

        Map<Integer, String> agentIdsMap = new HashMap<>();
        List<String> agentIdentifiers = instance.getAgentIdentifiers();
        if (agentIdentifiers != null && !agentIdentifiers.isEmpty()) {
            for (String agentIdentifier : agentIdentifiers) {
                // Get agent bean data (equivalent to AgentDataService.getAgentBeanData)
                AgentBean agentBean = agentDao.getAgentBeanData(agentIdentifier);
                if (agentBean == null) {
                    err = "Agent Identifier '" + agentIdentifier + "' does not exist";
                    log.error(err);
                    throw new ServerException(err);
                }
                agentIdsMap.put(agentBean.getId(), agentIdentifier);
            }
        }

        String name = instance.getName();
        if (name == null) {
            name = instance.getIdentifier();
        }

        Attributes hostAttribute = instance.getAttributes().stream()
                .filter(a -> (a.getName().equals("HostAddress")))
                .findAny().orElse(null);
        Attributes monitorPortAttribute = instance.getAttributes().stream()
                .filter(a -> (a.getName().equalsIgnoreCase("MonitorPort")))
                .findAny().orElse(null);

        if (hostAttribute == null) {
            err = "HostAddress attribute is missing for component instance name '" + name + "'";
            log.error(err);
            throw new ServerException(err);
        }

        // Check if the environment provided in the input is included in our supported subtypes
        int environmentTypeIdFromTypeName = environmentDao.getEnvTypeIdFromTypeName("Environment");
        if (environmentTypeIdFromTypeName == 0) {
            String errMsg = String.format("Could not find the type id for the type name %s in Data source.", "Environment");
            log.error(errMsg);
            throw new ServerException(errMsg);
        }

        List<ObjPojo> envSubTypeDetails = environmentDao.getEnvSubTypeDetails(environmentTypeIdFromTypeName);

        ObjPojo envType = envSubTypeDetails.stream()
                .filter(f -> f.getName().equalsIgnoreCase(instance.getEnvironment()))
                .findAny()
                .orElseGet(() -> {
                    String errMsg = String.format("The environment detail provided in the input %s is not among the supported subtypes for type %s. Marking environment value as 'NONE'.", instance.getEnvironment(), "Environment");
                    log.error(errMsg);
                    return envSubTypeDetails.stream()
                            .filter(f -> f.getName().equalsIgnoreCase("NONE"))
                            .findAny()
                            .orElseThrow(() -> new IllegalArgumentException("NONE subtype not found."));
                });

        int isDR = envType.getId();

        // Validate if the combination of Host address & environment exist
        if (isHost == 1) {
            int instanceDetailsByHostAddress = componentInstanceDao.getInstanceDetailsByHostAddress(accountId, hostAttribute.getValue(), isDR);

            if (instanceDetailsByHostAddress == -1) {
                String errMsg = String.format("Exception while getting count of the instances with host_address %s and environment %s for account %s from Data source", hostAttribute.getValue(), isDR, accountId);
                log.error(errMsg);
                throw new ServerException(errMsg);
            }

            if (instanceDetailsByHostAddress >= 1) {
                err = "Combination of host address and environment already exist for account.";
                log.error(err);
                throw new ServerException(err);
            }

            log.info("Host Address {} is being added for the first time. Continuing with request processing.", hostAttribute.getValue());

        } else {
            // Host address:MonitorPort match at account level for component instance
            if (monitorPortAttribute != null && !com.appnomic.appsone.common.util.StringUtils.isEmpty(monitorPortAttribute.getValue())) {
                int hostPortExistCount = componentInstanceDao.checkHostAddressMonitorPortExistance(accountId, hostAttribute.getValue(), monitorPortAttribute.getValue());

                if (hostPortExistCount > 0) {
                    err = "Component Instance's host address and monitor port already exists in account with id " + accountId + ".";
                    log.error(err);
                    throw new ServerException(err);
                } else if (hostPortExistCount == -1) {
                    err = "Exception while checking for host_address and port existence for account with id " + accountId + ".";
                    log.error(err);
                    throw new ServerException(err);
                }
            }
        }

        // Check whether host address exists while creating comp_instance
        if (componentBean.getComponentTypeId() != 1) {
            int hostExistCount = componentInstanceDao.checkHostAddressExistance(accountId, hostAttribute.getValue());
            if (hostExistCount == 0) {
                log.error("Component Instance mapped to host address does not exists in account with id {}", accountId);
                throw new ServerException("Host address does not exist");
            }
        }

        // Cluster component match at service level
        if (serviceId != null) {
            for (int sId : serviceId) {
                List<InstanceClusterServicePojo> instanceDetails = componentInstanceDao.getInstanceClusterServiceDetailsPojo(sId, accountId);
                if (isHost == 1) {
                    // Cluster component match at service level for host instance
                    if (instanceDetails != null && !instanceDetails.isEmpty()) {
                        for (InstanceClusterServicePojo singleHostDetails : instanceDetails) {
                            if (singleHostDetails.getClusterComponentId() != componentBean.getId()
                                    && singleHostDetails.getClusterComponentTypeId() == hostComponentTypeId) {
                                err = "Host Instance's component is different from existing host " +
                                        "cluster's component for the service id " + sId + ".";
                                log.error(err);
                                throw new ServerException(err);
                            }

                            if (singleHostDetails.getClusterCommonVersionId() != componentBean.getCommonVersionId()
                                    && singleHostDetails.getClusterComponentTypeId() == hostComponentTypeId) {
                                err = "Host Instance's component common version is different from existing host " +
                                        "cluster's component common version for the service id " + sId + ".";
                                log.error(err);
                                throw new ServerException(err);
                            }
                        }
                    }
                } else {
                    // Cluster component match at service level for component instance
                    if (instanceDetails != null && !instanceDetails.isEmpty()) {
                        for (InstanceClusterServicePojo singleCompInstanceDetails : instanceDetails) {
                            if (singleCompInstanceDetails.getClusterComponentId() != componentBean.getId()
                                    && singleCompInstanceDetails.getClusterComponentTypeId() != hostComponentTypeId) {
                                err = "Component Instance's component is different from existing component " +
                                        "cluster's component for the service id " + sId + ".";
                                log.error(err);
                                throw new ServerException(err);
                            }

                            if (singleCompInstanceDetails.getClusterCommonVersionId() != componentBean.getCommonVersionId()
                                    && singleCompInstanceDetails.getClusterComponentTypeId() != hostComponentTypeId) {
                                err = "Component Instance's component common version is different from existing Component " +
                                        "cluster's component common version for the service id " + sId + ".";
                                log.error(err);
                                throw new ServerException(err);
                            }
                        }
                    }
                }
            }
        }

        return ComponentInstanceBean.builder()
                .name(name)
                .identifier(instance.getIdentifier())
                .isDR(isDR)
                .mstComponentId(componentBean.getId())
                .mstComponentName(instance.getComponentName())
                .mstComponentVersionId(componentBean.getComponentVersionId())
                .mstComponentVersion(componentBean.getComponentVersionName())
                .mstComponentTypeId(componentBean.getComponentTypeId())
                .mstComponentType(componentBean.getComponentTypeName())
                .mstCommonVersionId(componentBean.getCommonVersionId())
                .mstCommonVersionName(componentBean.getCommonVersionName())
                .createdTime(DateTimeUtil.getTimeInGMT(System.currentTimeMillis()))
                .updatedTime(DateTimeUtil.getTimeInGMT(System.currentTimeMillis()))
                .userDetailsId(userId)
                .hostAddress(hostAttribute.getValue())
                .accountId(accountId)
                .discovery(instance.getDiscovery())
                .serviceIds(serviceId)
                .serviceIdentifiers(instance.getServiceIdentifiers())
                .parentIdentifier(instance.getParentIdentifier())
                .parentName(instance.getParentName())
                .isHost(isHost)
                .status(1)
                .agentIdsMap(agentIdsMap)
                .agentIdentifiers(instance.getAgentIdentifiers())
                .isPod(isPod)
                .attributes(getComponentAttributesListBean(instance, componentBean.getId(), componentBean.getCommonVersionId(), userId))
                .supervisorId(instance.getSupervisorId())
                .build();
    }

    /**
     * Creates service mappings for the component instance.
     * This creates tag mappings to link the component instance with services.
     */
    private void createServiceMappings(int instanceId, List<String> serviceIdentifiers, Account account) throws HealControlCenterException {
        try {
            // Get the service tag details for creating mappings
            com.heal.controlcenter.beans.TagDetailsBean serviceTagDetails = accountServiceDao.getTagDetails(Constants.SERVICE_TAG, account.getId());
            if (serviceTagDetails == null) {
                log.warn("Service tag details not found for account: {}", account.getId());
                return;
            }

            for (String serviceIdentifier : serviceIdentifiers) {
                // Get service details to get service ID
                Service service = serviceRepo.getServiceConfigurationByIdentifier(account.getIdentifier(), serviceIdentifier);
                if (service != null) {
                    // Create tag mapping between component instance and service
                    boolean exists = tagMappingBL.checkTagMappingExists(
                            serviceTagDetails.getId(),
                            instanceId,
                            Constants.COMP_INSTANCE_TABLE,
                            String.valueOf(service.getId()),
                            serviceIdentifier,
                            account.getId()
                    );

                    if (!exists) {
                        int tagMappingId = tagMappingBL.addTagMapping(
                                serviceTagDetails.getId(),
                                instanceId,
                                Constants.COMP_INSTANCE_TABLE,
                                String.valueOf(service.getId()),
                                serviceIdentifier,
                                "system", // Default user for system operations
                                account.getId()
                        );

                        if (tagMappingId == -1) {
                            log.error("Failed to create service mapping for instance: {} and service: {}", instanceId, serviceIdentifier);
                            throw new HealControlCenterException("Failed to create service mapping for service: " + serviceIdentifier);
                        } else {
                            log.debug("Successfully created service mapping with ID: {} for instance: {} and service: {}",
                                    tagMappingId, instanceId, serviceIdentifier);
                        }
                    } else {
                        log.debug("Service mapping already exists for instance: {} and service: {}", instanceId, serviceIdentifier);
                    }
                } else {
                    log.error("Service not found with identifier: {}", serviceIdentifier);
                    throw new HealControlCenterException("Service not found with identifier: " + serviceIdentifier);
                }
            }

            log.info("Successfully created service mappings for instance: {} with {} services", instanceId, serviceIdentifiers.size());

        } catch (Exception e) {
            log.error("Error creating service mappings for instance: {}", instanceId, e);
            throw new HealControlCenterException("Error creating service mappings: " + e.getMessage());
        }
    }

    /**
     * Gets controllers by type bypassing cache - exact equivalent of CommonUtils.getControllersByTypeBypassCache.
     * Uses DAO layer for data access.
     *
     * @param serviceType The parameter identifies the type of controller (e.g., "Services")
     * @param accountId The parameter identifies the Id of any account
     * @return List of controller, it can be application or services
     */
    private List<Controller> getControllersByTypeBypassCache(String serviceType, int accountId) {
        List<Controller> filtratedControllerList = new ArrayList<>();
        try {
            // Get the service mst subtype (equivalent to MasterCache.getMstTypeForSubTypeName)
            ViewTypesBean subTypeBean = masterComponentDao.getMstTypeForSubTypeName(Constants.CONTROLLER_TYPE_NAME_DEFAULT, serviceType, accountId);

            // Get all controllers for accountId bypassing cache (equivalent to MasterDataService.getControllerList)
            List<Controller> controllerList = masterComponentDao.getControllerList(accountId);

            // Filter with controller_type_id
            if (subTypeBean != null) {
                filtratedControllerList = controllerList.stream()
                        .filter(t -> t.getControllerTypeId() == subTypeBean.getSubTypeId())
                        .collect(Collectors.toList());
            }

        } catch (Exception e) {
            log.error("Error occurred while fetching controller details for service name: {} account id: {}", serviceType, accountId, e);
        }
        return filtratedControllerList;
    }

    /**
     * Validates that component exists and returns component details.
     */
    private MasterComponentBean validateComponentExists(String componentName, String componentVersion, int accountId) throws ServerException {
        try {
            // Get component details from master data
            MasterComponentBean componentBean = masterComponentDao.findByNameVersionAndAccountId(componentName, componentVersion, accountId);
            if (componentBean == null) {
                String err = "Component with name '" + componentName + "' and version '" + componentVersion + "' doesn't exist.";
                log.error(err);
                throw new ServerException(err);
            }
            return componentBean;
        } catch (Exception e) {
            log.error("Error validating component: {}", e.getMessage(), e);
            throw new ServerException("Component validation failed: " + e.getMessage());
        }
    }

    /**
     * Gets service IDs from service list - exact equivalent of ComponentInstanceUtil.getServiceIds.
     * Converted from JDBI to JDBC.
     */
    private int[] getServiceIds(List<Controller> serviceList, List<String> serviceIdentifiers) throws ServerException {
        int[] serviceIds = new int[serviceIdentifiers.size()];
        String err;

        if (serviceList != null && !serviceIdentifiers.isEmpty()) {
            int i = 0;
            for (String svcIdentifier : serviceIdentifiers) {
                Controller service = serviceList.stream()
                        .filter(c -> (c.getIdentifier().equals(svcIdentifier.trim())) && c.getStatus() == 1)
                        .findAny().orElse(null);
                if (service == null) {
                    err = "Service Identifier '" + svcIdentifier + "' does not exist";
                    log.error(err);
                    throw new ServerException(err);
                }
                serviceIds[i++] = Integer.parseInt(service.getAppId());
            }
        }
        return serviceIds;
    }

    /**
     * Validates agent identifiers and returns agent IDs map.
     */
    private Map<Integer, String> validateAgentIdentifiers(List<String> agentIdentifiers) throws ServerException {
        Map<Integer, String> agentIdsMap = new HashMap<>();
        if (agentIdentifiers != null && !agentIdentifiers.isEmpty()) {
            for (String agentIdentifier : agentIdentifiers) {
                // For now, we'll create a placeholder validation
                // In a real implementation, this would validate against agent data service
                log.debug("Validating agent identifier: {}", agentIdentifier);
                // agentIdsMap.put(agentId, agentIdentifier);
            }
        }
        return agentIdsMap;
    }

    /**
     * Validates host address attribute exists and returns the host address.
     */
    private String validateHostAddressAttribute(ComponentInstancePojo instance) throws ServerException {
        if (instance.getAttributes() == null) {
            throw new ServerException("Host address attribute is missing for component instance '" + instance.getName() + "'");
        }

        Attributes hostAttribute = instance.getAttributes().stream()
                .filter(a -> "HostAddress".equals(a.getName()) || "Host Address".equals(a.getName()))
                .findFirst()
                .orElse(null);

        if (hostAttribute == null || StringUtils.isEmpty(hostAttribute.getValue())) {
            throw new ServerException("Host address attribute is missing for component instance '" + instance.getName() + "'");
        }

        return hostAttribute.getValue();
    }

    /**
     * Performs host/component specific validations.
     */
    private void performHostComponentValidations(ComponentInstancePojo instance, int accountId, int isHost,
                                               String hostAddress, int isDR, int[] serviceIds,
                                               MasterComponentBean componentBean) throws ServerException {
        // Host address and environment combination validation for host instances
        if (isHost == 1) {
            // Check if combination of host address and environment already exists
            boolean exists = componentInstanceDao.checkHostAddressEnvironmentExists(accountId, hostAddress, isDR);
            if (exists) {
                throw new ServerException("Combination of host address and environment already exist for account.");
            }
        } else {
            // For component instances, validate host address and monitor port combination
            Attributes monitorPortAttribute = instance.getAttributes().stream()
                    .filter(a -> "MonitorPort".equalsIgnoreCase(a.getName()))
                    .findFirst()
                    .orElse(null);

            if (monitorPortAttribute != null && !StringUtils.isEmpty(monitorPortAttribute.getValue())) {
                boolean exists = componentInstanceDao.checkHostAddressPortExists(accountId, hostAddress, monitorPortAttribute.getValue());
                if (exists) {
                    throw new ServerException("Component Instance's host address and monitor port already exists in account.");
                }
            }

            // Check if host address exists (component instances must be mapped to existing hosts)
            if (componentBean.getComponentTypeId() != 1) { // Not a host component
                boolean hostExists = componentInstanceDao.checkHostAddressExists(accountId, hostAddress);
                if (!hostExists) {
                    throw new ServerException("Host address does not exist");
                }
            }
        }

        // Cluster component validation at service level
        if (serviceIds != null) {
            for (int serviceId : serviceIds) {
                validateClusterComponentAtServiceLevel(serviceId, accountId, isHost, componentBean);
            }
        }
    }

    /**
     * Validates cluster component consistency at service level.
     */
    private void validateClusterComponentAtServiceLevel(int serviceId, int accountId, int isHost,
                                                       MasterComponentBean componentBean) throws ServerException {
        // Get existing instance details for the service
        List<InstanceClusterServiceBean> instanceDetails =
                componentInstanceDao.getInstanceClusterServiceDetails(serviceId, accountId);

        if (instanceDetails != null && !instanceDetails.isEmpty()) {
            for (InstanceClusterServiceBean existingInstance : instanceDetails) {
                if (isHost == 1) {
                    // Host instance validation
                    if (existingInstance.getClusterComponentId() != componentBean.getId()
                            && existingInstance.getClusterComponentTypeId() == 1) { // Host component type
                        throw new ServerException("Host Instance's component is different from existing host cluster's component for the service id " + serviceId);
                    }

                    if (existingInstance.getClusterCommonVersionId() != componentBean.getCommonVersionId()
                            && existingInstance.getClusterComponentTypeId() == 1) {
                        throw new ServerException("Host Instance's component common version is different from existing host cluster's component common version for the service id " + serviceId);
                    }
                } else {
                    // Component instance validation
                    if (existingInstance.getClusterComponentId() != componentBean.getId()
                            && existingInstance.getClusterComponentTypeId() != 1) { // Not host component type
                        throw new ServerException("Component Instance's component is different from existing component cluster's component for the service id " + serviceId);
                    }

                    if (existingInstance.getClusterCommonVersionId() != componentBean.getCommonVersionId()
                            && existingInstance.getClusterComponentTypeId() != 1) {
                        throw new ServerException("Component Instance's component common version is different from existing Component cluster's component common version for the service id " + serviceId);
                    }
                }
            }
        }
    }

    /**
     * Gets component attributes list bean (equivalent to getComponentAttributesListBean in original).
     */
    private List<CompInstanceAttributesBean> getComponentAttributesListBean(
            ComponentInstancePojo instance, int mstComponentId, int mstCommonVersionId, String userId) throws ServerException {

        List<com.heal.controlcenter.beans.CompInstanceAttributesBean> attributesBeanList = new ArrayList<>();

        if (instance.getAttributes() != null) {
            for (Attributes attribute : instance.getAttributes()) {
                if (StringUtils.isEmpty(attribute.getName())) {
                    throw new ServerException("Attribute name is empty for component instance '" + instance.getIdentifier() + "'");
                }

                // Create attribute bean
                CompInstanceAttributesBean instanceAttributesBean =
                        CompInstanceAttributesBean.builder()
                                .attributeName(attribute.getName())
                                .attributeValue(attribute.getValue())
                                .userDetailsId(userId)
                                .build();

                attributesBeanList.add(instanceAttributesBean);
            }
        }

        return attributesBeanList;
    }

    /**
     * Performs cross-instance validations (equivalent to moreValidationsForEachFields in original).
     */
    private void performCrossInstanceValidations(List<ComponentInstanceBean> beanList) throws ServerException {
        List<ComponentInstanceBean> hostList = new ArrayList<>();
        List<ComponentInstanceBean> componentInstanceList = new ArrayList<>();

        // Separate host instances from component instances
        for (ComponentInstanceBean bean : beanList) {
            if (bean.getIsHost() == 1) {
                hostList.add(bean);
            } else {
                componentInstanceList.add(bean);
            }
        }

        // Validate host instances
        validateHostInstances(hostList);

        // Validate component instances
        validateComponentInstances(componentInstanceList);
    }

    /**
     * Validates host instances for duplicates and conflicts.
     */
    private void validateHostInstances(List<ComponentInstanceBean> hostList) throws ServerException {
        for (int i = 0; i < hostList.size() - 1; i++) {
            ComponentInstanceBean hostInstance1 = hostList.get(i);
            List<Integer> serviceList1 = Arrays.stream(hostInstance1.getServiceIds()).boxed()
                    .sorted(Comparator.comparing(Integer::intValue)).collect(Collectors.toList());

            for (int j = i + 1; j < hostList.size(); j++) {
                ComponentInstanceBean hostInstance2 = hostList.get(j);

                // Check for duplicate host addresses
                if (hostInstance1.getHostAddress().equals(hostInstance2.getHostAddress())) {
                    throw new ServerException("Multiple Host Instance with same host address found in request Body.");
                }

                List<Integer> serviceList2 = Arrays.stream(hostInstance2.getServiceIds()).boxed()
                        .sorted(Comparator.comparing(Integer::intValue)).collect(Collectors.toList());

                if (equateList(serviceList1, serviceList2)) {
                    if (hostInstance1.getMstComponentId() != hostInstance2.getMstComponentId()) {
                        throw new ServerException("Multiple Host Instance with different component name mapped to same service found in request Body.");
                    } else if (hostInstance1.getMstCommonVersionId() != hostInstance2.getMstCommonVersionId()) {
                        throw new ServerException("Multiple Host Instance with different common version mapped to same service found in request Body.");
                    }
                }
            }
        }
    }

    /**
     * Validates component instances for duplicates and conflicts.
     */
    private void validateComponentInstances(List<ComponentInstanceBean> componentInstanceList) throws ServerException {
        for (int i = 0; i < componentInstanceList.size() - 1; i++) {
            ComponentInstanceBean componentInstance1 = componentInstanceList.get(i);
            List<Integer> serviceList1 = Arrays.stream(componentInstance1.getServiceIds()).boxed()
                    .sorted(Comparator.comparing(Integer::intValue)).collect(Collectors.toList());

            String monitorPort1 = getMonitorPort(componentInstance1);

            for (int j = i + 1; j < componentInstanceList.size(); j++) {
                ComponentInstanceBean componentInstance2 = componentInstanceList.get(j);
                String monitorPort2 = getMonitorPort(componentInstance2);

                // Check for duplicate host address:port combinations
                if (monitorPort1 != null && monitorPort2 != null) {
                    if ((componentInstance1.getHostAddress() + ":" + monitorPort1)
                            .equals(componentInstance2.getHostAddress() + ":" + monitorPort2)) {
                        throw new ServerException("Multiple component Instance with same HostAddress:MonitorPort pair found in request Body.");
                    }
                }

                List<Integer> serviceList2 = Arrays.stream(componentInstance2.getServiceIds()).boxed()
                        .sorted(Comparator.comparing(Integer::intValue)).collect(Collectors.toList());

                if (equateList(serviceList1, serviceList2)) {
                    if (componentInstance1.getMstComponentId() != componentInstance2.getMstComponentId()) {
                        throw new ServerException("Multiple Component Instance with different component name mapped to same service found in request Body.");
                    } else if (componentInstance1.getMstCommonVersionId() != componentInstance2.getMstCommonVersionId()) {
                        throw new ServerException("Multiple Component Instance with different common version mapped to same service found in request Body.");
                    }
                }
            }
        }
    }

    /**
     * Gets monitor port from component instance attributes.
     */
    private String getMonitorPort(ComponentInstanceBean componentInstance) {
        if (componentInstance.getAttributes() != null) {
            for (com.heal.controlcenter.beans.CompInstanceAttributesBean attribute : componentInstance.getAttributes()) {
                if ("MonitorPort".equalsIgnoreCase(attribute.getAttributeName())) {
                    return attribute.getAttributeValue();
                }
            }
        }
        return null;
    }

    /**
     * Compares two lists for equality or overlap (equivalent to equateList in original).
     */
    private boolean equateList(List<Integer> list1, List<Integer> list2) {
        List<Integer> list3 = new ArrayList<>(list2);
        list3.retainAll(list1);

        if (list3.size() == list1.size()) {
            return true;
        } else {
            return list3.size() > 0;
        }
    }

    /**
     * Adds component instance to database - equivalent to ComponentInstanceUtil.addComponentInstance.
     * This method follows the exact pattern from appsone-controlcenter using JDBC operations.
     * @param bean Component instance bean
     * @return Instance ID
     * @throws Exception if insertion fails
     */
    private int addComponentInstanceToDatabase(ComponentInstanceBean bean) throws Exception {
        try {
            // Save component instance to database using JDBC
            int instanceId = componentInstanceDao.saveComponentInstance(bean);

            if (instanceId > 0) {
                log.info("Added comp instance for identifier: {}, name: {}", bean.getIdentifier(), bean.getName());
                bean.setId(instanceId);

                // Add attributes if present
                if (bean.getAttributes() != null && !bean.getAttributes().isEmpty()) {
                    addAttributes(bean);
                }

                // Add agent mapping if present
                if (bean.getAgentIdsMap() != null && !bean.getAgentIdsMap().isEmpty()) {
                    addAgentMapping(instanceId, bean.getAgentIdsMap());
                }
            } else {
                String err = "Failed to add the comp instance data for comp instance name: " + bean.getName();
                log.error(err);
                throw new Exception(err);
            }

            return instanceId;

        } catch (Exception e) {
            log.error("Error adding component instance: {}", e.getMessage(), e);
            throw e;
        }
    }

    /**
     * Adds component instance attributes to database.
     * @param bean Component instance bean
     * @throws Exception if insertion fails
     */
    private void addAttributes(ComponentInstanceBean bean) throws Exception {
        for (CompInstanceAttributesBean attribute : bean.getAttributes()) {
            attribute.setCompInstanceId(bean.getId());

            // Get the mst_common_attributes_id for the attribute name
            int mstCommonAttributesId = componentInstanceDao.getMstCommonAttributesIdByName(attribute.getAttributeName());
            if (mstCommonAttributesId == -1) {
                log.warn("Could not find mst_common_attributes_id for attribute: {}", attribute.getAttributeName());
                continue; // Skip this attribute if ID not found
            }
            attribute.setMstCommonAttributesId(mstCommonAttributesId);

            componentInstanceDao.saveComponentInstanceAttribute(attribute);
        }
    }

    /**
     * Adds agent mapping for component instance.
     * @param instanceId Component instance ID
     * @param agentIdsMap Agent IDs map
     * @throws Exception if insertion fails
     */
    private void addAgentMapping(int instanceId, Map<Integer, String> agentIdsMap) throws Exception {
        for (Map.Entry<Integer, String> entry : agentIdsMap.entrySet()) {
            componentInstanceDao.saveAgentInstanceMapping(instanceId, entry.getKey());
        }
    }



    /**
     * Adds instances to Redis cache - equivalent to ComponentInstanceUtil.addInstancesToRedis.
     * @param beanList List of component instance beans
     * @param accountIdentifier Account identifier
     * @return List of IdPojo objects
     * @throws Exception if Redis update fails
     */
    private List<IdPojo> addInstancesToRedis(List<ComponentInstanceBean> beanList, String accountIdentifier) throws Exception {
        try {
            // Get current instances from Redis
            List<CompInstClusterDetails> instanceDetails = instanceRepo.getInstances(accountIdentifier);
            if (instanceDetails == null) {
                instanceDetails = new ArrayList<>();
            }

            for (ComponentInstanceBean componentInstanceBean : beanList) {
                // Convert ComponentInstanceBean to CompInstClusterDetails for Redis
                CompInstClusterDetails clusterDetails = convertToCompInstClusterDetails(componentInstanceBean);
                instanceDetails.add(clusterDetails);

                // Update instance-specific Redis data
                instanceRepo.updateInstanceByIdentifier(accountIdentifier, clusterDetails);

                // Update service-wise instance mappings
                updateServiceWiseInstanceMappings(accountIdentifier, componentInstanceBean);
            }

            // Update complete instances list in Redis
            instanceRepo.updateInstances(accountIdentifier, instanceDetails);

            log.info("Successfully updated Redis cache for {} instances in account: {}", beanList.size(), accountIdentifier);

        } catch (Exception e) {
            log.error("Error updating Redis cache: {}", e.getMessage(), e);
            throw e;
        }

        // Build and return IdPojo list - equivalent to original return structure
        List<IdPojo> idPojosList = new ArrayList<>();
        for (ComponentInstanceBean bean : beanList) {
            idPojosList.add(IdPojo.builder()
                    .id(bean.getId())
                    .name(bean.getName())
                    .identifier(bean.getIdentifier())
                    .build());
        }

        return idPojosList;
    }

    /**
     * Converts ComponentInstanceBean to CompInstClusterDetails for Redis storage.
     * @param bean Component instance bean
     * @return CompInstClusterDetails object
     */
    private CompInstClusterDetails convertToCompInstClusterDetails(ComponentInstanceBean bean) {
        return CompInstClusterDetails.builder()
                .id(bean.getId())
                .name(bean.getName())
                .identifier(bean.getIdentifier())
                .status(bean.getStatus())
                .createdTime(bean.getCreatedTime())
                .updatedTime(bean.getUpdatedTime())
                .lastModifiedBy(bean.getUserDetailsId())
                .componentId(bean.getMstComponentId())
                .componentName(bean.getMstComponentName())
                .componentTypeId(bean.getMstComponentTypeId())
                .componentTypeName(bean.getMstComponentType())
                .componentVersionId(bean.getMstComponentVersionId())
                .componentVersionName(bean.getMstComponentVersion())
                .commonVersionId(bean.getMstCommonVersionId())
                .commonVersionName(bean.getMstCommonVersionName())
                .supervisorId(bean.getSupervisorId())
                .hostAddress(bean.getHostAddress())
                .isDR(bean.getIsDR())
                .discovery(bean.getDiscovery())
                .agentIds(bean.getAgentIdentifiers() != null ? bean.getAgentIdentifiers() : new ArrayList<>())
                .accountId(bean.getAccountId())
                .isCluster(false)
                .build();
    }

    /**
     * Updates service-wise instance mappings in Redis.
     * @param accountIdentifier Account identifier
     * @param bean Component instance bean
     * @throws Exception if update fails
     */
    private void updateServiceWiseInstanceMappings(String accountIdentifier, ComponentInstanceBean bean) throws Exception {
        if (bean.getServiceIdentifiers() != null) {
            // TODO: Implement service-wise instance mapping when the methods are available in InstanceRepo
            // For now, we'll just log that service mappings would be updated
            log.debug("Service-wise instance mappings would be updated for instance: {} with services: {}",
                    bean.getIdentifier(), bean.getServiceIdentifiers());
        }
    }
}
